import mixpanel from 'mixpanel-browser'
import { useEffect } from 'react'
import { envs } from '../../config/envs'

const mixpanelProxy = envs.mixPanelProxy

/**
 * Handles mixpanel tracking initialization and handles tracking consent
 */
export const useTracking = () => {
  useEffect(() => {
    mixpanel.init(import.meta.env.VITE_MIX_PANEL_API_KEY ?? '', {
      persistence: 'cookie',
      // GDPR Needs to be opt out by default, DO NOT MODIFY!
      opt_out_tracking_by_default: true,
      api_host: mixpanelProxy,
      record_heatmap_data: true,
      record_sessions_percent: 0.6,
      record_mask_text_selector: '',
    })
  }, [])
}
