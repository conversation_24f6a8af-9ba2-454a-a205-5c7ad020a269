import { useLocation } from 'react-router'
import { ACCOUNT_MENU, DASHBOARD_NAVIGATION, PRIVATE } from '../../routes/Route'
import { ASSET } from '../constants/Assets'
import { UI_TEST_ID } from '../constants/DataTestIDs'
import { useDeviceScreen } from '../hooks/useDeviceScreen'
import { useTranslate } from '../hooks/useTranslate'
import { MobileBarItemsProps } from '../types/MobileBarItems.types'
import MobileBarItem from './MobileBarItem'

/**
 * Mobile bar items that change styling depending on the
 * active route
 */
const MobileBarItems = ({
  completedKyc,
  isAuthenticated,
  className,
}: MobileBarItemsProps) => {
  const t = useTranslate()
  const location = useLocation()
  const { isMobileOrTablet } = useDeviceScreen()

  const isDesktopAccountOverride = () =>
    !isMobileOrTablet && location.pathname.includes(PRIVATE.ACCOUNT)

  const isDesktopDashboardOverride = () =>
    !isMobileOrTablet && location.pathname.includes(PRIVATE.MYTT_DASHBOARD)

  return (
    <article className={className}>
      <MobileBarItem
        to={
          // On mobile this needs to lead to INDEX route
          isMobileOrTablet
            ? PRIVATE.MYTT_DASHBOARD
            : DASHBOARD_NAVIGATION.FUNDED_PROGRESS
        }
        buttonLabel={t('ACCOUNT.BUTTON_NAVIGATION_MYTONTINE')}
        iconName={ASSET.iconbottomnamt}
        activeIcon={ASSET.actieiconbottomnamt}
        dataTestID={UI_TEST_ID.rootTtDashboard}
        overrideActive={isDesktopDashboardOverride()}
      />

      {isAuthenticated && (
        <MobileBarItem
          //Opens personal details with Account navigation
          to={
            // On mobile this needs to lead to INDEX route
            isMobileOrTablet ? PRIVATE.ACCOUNT : ACCOUNT_MENU.PERSONAL_DETAILS
          }
          buttonLabel={t('ACCOUNT.BUTTON_NAV_2')}
          iconName={ASSET.iconbottomnaaccount}
          notification={!completedKyc}
          activeIcon={ASSET.actieiconbottomnaaccount}
          dataTestID={UI_TEST_ID.rootAccount}
          overrideActive={isDesktopAccountOverride()}
        />
      )}
    </article>
  )
}

export default MobileBarItems
