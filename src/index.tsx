import {
  browserTracingIntegration,
  init,
  replayIntegration,
} from '@sentry/react'
import { createRoot } from 'react-dom/client'
import App from './App'
import { ENVIRONMENTS } from './common/utils/consts'
import {
  consoleWarningMessage,
  getIpGeoLocation,
} from './common/utils/UtilFunctions'
import { envs } from './config/envs'

const { environment, envColor } = envs

init({
  dsn: 'https://<EMAIL>/5962352',
  integrations: [
    browserTracingIntegration(),
    replayIntegration({
      maskAllText: false,
    }),
  ],
  environment,
  tracesSampleRate: 0.6,
  replaysSessionSampleRate: 0.6,
  enabled: environment === 'production' || environment === 'staging',
})

// Won't work on localhost or local dev env
if (environment !== ENVIRONMENTS.development) {
  getIpGeoLocation()
}

if (environment !== ENVIRONMENTS.development) {
  consoleWarningMessage()
}

console.log(
  `Using config: %c${environment || 'Vite local dev server'}`,
  `color:${envColor}`
)

const container = document.querySelector('#root')
// biome-ignore lint/style/noNonNullAssertion: <TODO: Find solution maybe>
const root = createRoot(container!)

root.render(<App />)
