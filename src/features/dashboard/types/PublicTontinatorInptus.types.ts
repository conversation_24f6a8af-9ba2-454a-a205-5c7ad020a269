import { ReactNode } from 'react'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { TontinatorInputsProps } from './TontinatorInputs.types'

type PublicTontinatorInputsProps = {
  incomeForecastParams: IncomeForecastParams
  setIncomeForecastParams: (params: IncomeForecastParams) => void
  comparison: boolean
  blueForecastParams: IncomeForecastParams
  setBlueForecastParams: (params: IncomeForecastParams) => void
  yellowForecastParams: IncomeForecastParams
  setYellowForecastParams: (params: IncomeForecastParams) => void
  extendYellow?: ReactNode
  extendBlue?: ReactNode
  extendDefault?: ReactNode
  propsForDefaultLayout?: Partial<TontinatorInputsProps>
  propsForBlueLayout?: Partial<TontinatorInputsProps>
  propsForYellowLayout?: Partial<TontinatorInputsProps>
}

export type { PublicTontinatorInputsProps }
