import { SliderAdjustmentProps } from '../types/SliderAdjustments.types'
import {
  adjustContributions,
  adjustCurrentAgeSliderInsideRetirementAgeSlider,
  adjustMonthlyContribution,
  adjustOneTimeContribution,
  adjustRetirementAgeSlider,
  adjustRetirementAgeViaCurrentAgeCall,
  checkRetirementAgeIsBelowMin,
} from '../utils/UtilFunctions'

/**
 * Handles slider state and makes necessary adjustments to the state
 */
export const useSliderAdjustmentV2 = ({
  formData,
  setFormData,
  params,
  skipComparePlanRangeAdjustment,
}: SliderAdjustmentProps) => {
  let { minRetirementAge, maxRetirementAge } = params

  if (skipComparePlanRangeAdjustment) {
    const minAge = Math.max(
      minRetirementAge?.age ?? 0,
      formData?.contributionAge?.age ?? 0
    )
    minRetirementAge = {
      ...minRetirementAge,
      month: minRetirementAge?.month ?? 0,
      age: minAge,
    }
  }

  /**
   * <PERSON>les current age slider `onChange` event. Before the values are
   * committed to state adjustments are done. The adjustments that this function
   * does are:
   * - Moves the retirement slider if the retirement age is below the current
   *   age `adjustRetirementAgeViaCurrentAgeCall`
   * - Adjusts contribution values if 0 and the user is retired (`currentAge`
   *   === `retirementAge`)
   */
  const handleCurrentAge = (currentAge: number) => {
    const isRetired =
      adjustRetirementAgeSlider(
        currentAge,
        formData?.retirementAge?.age ?? 0,
        minRetirementAge?.age ?? 0,
        maxRetirementAge?.age ?? 0
      ) === currentAge

    //Makes sure the current age slider follow the retirement age slider
    const adjustedRetirementAge = adjustRetirementAgeViaCurrentAgeCall(
      formData?.retirementAge?.age ?? 0,
      currentAge,
      minRetirementAge?.age ?? 0,
      maxRetirementAge?.age ?? 0
    )

    const adjustedFormData = adjustContributions({
      isRetired,
      formData: {
        ...formData,
        contributionAge: {
          age: currentAge,
          month: 0,
        },
        retirementAge: {
          age: adjustedRetirementAge,
          month: 0,
        },
      },
      params,
    })

    setFormData(adjustedFormData)
  }

  /**
   * Handles retirement age slider `onChange` event. Before the values are
   * committed to state adjustments are done. The adjustments that this function
   * does are:
   * - Prevents the retirement age going below the current age
   *   `adjustRetirementAgeSlider`
   * - Prevents the retired age going below the the minimum retirement age
   *   `adjustRetirementAgeSlider`
   * - Adjusts contribution values if 0 and the user is retired (`currentAge`
   *   === `retirementAge`)
   */
  const handleRetirementAge = (retirementAge: number) => {
    const isRetired =
      checkRetirementAgeIsBelowMin(
        retirementAge,
        formData.contributionAge.age,
        minRetirementAge?.age ?? 0
      ) === formData.contributionAge.age

    const adjustedRetirementAge = adjustRetirementAgeSlider(
      formData.contributionAge.age,
      retirementAge,
      minRetirementAge?.age ?? 0,
      maxRetirementAge?.age ?? 0
    )

    const adjustedContributionAge =
      adjustCurrentAgeSliderInsideRetirementAgeSlider(
        formData.contributionAge.age,
        adjustedRetirementAge,
        minRetirementAge?.age ?? 0,
        maxRetirementAge?.age ?? 0
      )

    const adjustedFormData = adjustContributions({
      isRetired,
      formData: {
        ...formData,
        retirementAge: { age: adjustedRetirementAge, month: 0 },
        contributionAge: { age: adjustedContributionAge, month: 0 },
      },
      params,
    })

    setFormData(adjustedFormData)
  }

  /**
   * Handles the monthly contribution slider `onChange` event.
   * Before the value is committed to the state it adjustments are made with
   */
  const handleOneTime = (oneTimeContribution: number) => {
    const adjustedMonthlyContribution = adjustMonthlyContribution(
      formData.monthlyContribution,
      oneTimeContribution,
      params
    )

    const adjustedData = {
      ...formData,
      oneTimeContribution,
      monthlyContribution: adjustedMonthlyContribution,
    }

    setFormData(adjustedData)
  }

  /**
   * Handles the monthly contribution slider `onChange` event. Before the value
   * is committed to the state it adjustments are made with
   */
  const handleMonthly = (monthlyContribution: number) => {
    const adjustedOneTimeContribution = adjustOneTimeContribution(
      monthlyContribution,
      formData.oneTimeContribution,
      params
    )

    const adjustedFormData = {
      ...formData,
      monthlyContribution,
      oneTimeContribution: adjustedOneTimeContribution,
    }

    setFormData(adjustedFormData)
  }

  return {
    handleCurrentAge,
    handleRetirementAge,
    handleOneTime,
    handleMonthly,
  }
}
