import { useState } from 'react'
import Button from '../../../common/components/Button'
import Layout from '../../../common/components/Layout'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import ComparePlanButtons from '../components/ComparePlanButtons'
import SandboxDashboard from '../components/SandboxDashboard'
import { useEmbeddedTon } from '../hooks/useEmbededTon'
import {
  chooseDefaultParams,
  useForecastParamsState,
} from '../hooks/usePreRegisterForecast'
import style from '../style/SandboxDashboard.module.scss'

const SandboxTontinatorPage = () => {
  const { isEmbedded } = useEmbeddedTon()
  const {
    isAuthenticated,
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()
  const { detectedCountry } = useLocalization()
  const { isMobileOrTablet } = useDeviceScreen()

  const navigation = useCustomNavigation()
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: user_details?.residency ?? detectedCountry?.alpha3,
  })
  const [isCompareOpen, setIsCompareOpen] = useState(false)
  const [isParamsOpen, setIsParamsOpen] = useState(false)
  const { tontinatorParams } = supportedCountry

  const defaultParams = chooseDefaultParams({
    tontinatorParams,
    supportedCountry,
    isAuthenticated,
    userDetails: user_details,
  })

  const {
    blueForecastParams,
    yellowForecastParams,
    incomeForecastParams,
    setBlueForecastParams,
    setYellowForecastParams,
    setIncomeForecastParams,
  } = useForecastParamsState({ defaultParams, tontinatorParams })

  return (
    <Layout
      hideMobileHeader={isEmbedded}
      hideDividerHeader={isEmbedded}
      pageTitle={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
      containerMt="mt-20"
      containerHeight="mh"
      onClickAction={() => {
        // When slider page is open, close it
        if (isParamsOpen) {
          setIsParamsOpen(false)
        }
        // When compare page is open close it
        if (isCompareOpen) {
          setIsCompareOpen(false)
        }
        // When on the compare page and the slider compare page is open
        // close close only the slider page
        if (isParamsOpen && isCompareOpen) {
          setIsParamsOpen(false)
          setIsCompareOpen(true)
        }
        if (!isParamsOpen && !isCompareOpen) {
          navigation(PUBLIC.GO_TO_PREV_PAGE)
        }
      }}
      bottomSection={
        <section className={style['sandbox-dashboard__bottom-layout']}>
          <div className={style['sandbox-dashboard__buttons']}>
            {!isCompareOpen && isMobileOrTablet && (
              <Button
                variant="alternative"
                onClick={() => setIsParamsOpen(true)}
              >
                {t('CHECK_OTHER_SCENARIOS')}
              </Button>
            )}

            {!isCompareOpen && (
              <Button variant="blue" onClick={() => setIsCompareOpen(true)}>
                {t('BUTTON_TO_COMPARE')}
              </Button>
            )}

            {isCompareOpen && (
              // We use MTL buttons here because there is no design for the full
              // version to what does what
              <>
                {isMobileOrTablet && (
                  <Button
                    variant="alternative"
                    onClick={() => setIsParamsOpen(true)}
                  >
                    {t('CHECK_OTHER_SCENARIOS')}
                  </Button>
                )}
                <ComparePlanButtons
                  onClickBack={() => setIsCompareOpen(false)}
                  onClickPlan1={() => {
                    setIsCompareOpen(false)
                    setIncomeForecastParams(blueForecastParams)
                  }}
                  onClickPlan2={() => {
                    setIsCompareOpen(false)
                    setIncomeForecastParams(yellowForecastParams)
                  }}
                  blueForecastParams={blueForecastParams}
                  yellowForecastParams={yellowForecastParams}
                />
              </>
            )}
          </div>
        </section>
      }
    >
      <SandboxDashboard
        blueForecastParams={blueForecastParams}
        yellowForecastParams={yellowForecastParams}
        incomeForecastParams={incomeForecastParams}
        setBlueForecastParams={setBlueForecastParams}
        setYellowForecastParams={setYellowForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
        isCompareOpen={isCompareOpen}
        isParamsOpen={isParamsOpen}
        setIsParamsOpen={setIsParamsOpen}
        isEmbedded={isEmbedded}
      />
    </Layout>
  )
}

export default SandboxTontinatorPage
