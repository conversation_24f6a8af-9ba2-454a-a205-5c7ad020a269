import Button from '../../../common/components/Button'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import sandboxStyle from '../style/SandboxDashboard.module.scss'
import tontinatorInputsModalStyle from '../style/TontinatorInputsModal.module.scss'
import { SandboxDashboardProps } from '../types/SandboxDashboard.types'
import InvStrategiesDropdown from './InvStrategiesDropdown'
import PensionPlanDashboard from './PensionPlanDashboard'
import PublicTontinatorInputs from './PublicTontinatorInputs'
import PublicTontinatorInputsModal from './PublicTontinatorInputsModal'
import TontinatorDashboard from './TontinatorDashboard'

const strategyChosenKey = 'strategyChosen'

const SandboxDashboard = ({
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  isCompareOpen,
  isParamsOpen,
  setIsParamsOpen,
  isEmbedded = false,
}: SandboxDashboardProps) => {
  const t = useTranslate()
  const { storedValue, addValueToStorage } = useBrowserStorage({
    key: strategyChosenKey,
  })
  const { isMobileOrTablet } = useDeviceScreen()
  const { isAuthenticated } = useAccountService()

  const inputsParams = {
    blueForecastParams: blueForecastParams,
    comparison: isCompareOpen,
    incomeForecastParams: incomeForecastParams,
    setIncomeForecastParams: setIncomeForecastParams,
    setBlueForecastParams: setBlueForecastParams,
    setYellowForecastParams: setYellowForecastParams,
    yellowForecastParams: yellowForecastParams,
    isEmbedded: isEmbedded,
    extendDefault: (
      <InvStrategiesDropdown
        defaultOpen={!storedValue}
        value={incomeForecastParams?.strategy}
        onChange={(strategy) => {
          setIncomeForecastParams({
            ...incomeForecastParams,
            strategy,
          })
          addValueToStorage('true')
        }}
        label={t('TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL')}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
        className={sandboxStyle['sandbox-dashboard__investment-strategy']}
      />
    ),
    extendBlue: (
      <InvStrategiesDropdown
        value={blueForecastParams?.strategy}
        onChange={(strategy) => {
          setBlueForecastParams({
            ...blueForecastParams,
            strategy,
          })
        }}
        label={t('TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL')}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <InvStrategiesDropdown
        value={yellowForecastParams?.strategy}
        onChange={(strategy) => {
          setYellowForecastParams({
            ...yellowForecastParams,
            strategy,
          })
        }}
        label={t('TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL')}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    propsForDefaultLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
    propsForBlueLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
    propsForYellowLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
  }

  return (
    <section className={sandboxStyle['sandbox-dashboard__dashboards']}>
      {isCompareOpen ? (
        <PensionPlanDashboard
          dataToDraw={[blueForecastParams, yellowForecastParams]}
          isEmbedded={isEmbedded}
        />
      ) : (
        <TontinatorDashboard incomeForecastParams={incomeForecastParams} />
      )}
      {!isMobileOrTablet && <PublicTontinatorInputs {...inputsParams} />}
      {isMobileOrTablet && isParamsOpen && (
        <PublicTontinatorInputsModal
          isOpen={isParamsOpen}
          tontinatorProps={inputsParams}
          className={tontinatorInputsModalStyle['tontinator-inputs-modal']}
        >
          <div
            className={
              tontinatorInputsModalStyle[
                'tontinator-inputs-modal__buttons-wrapper'
              ]
            }
          >
            <Button
              onClick={() => setIsParamsOpen(false)}
              variant="alternative"
            >
              {t('CHECK_UPDATED_CHART')}
            </Button>
          </div>
        </PublicTontinatorInputsModal>
      )}
    </section>
  )
}

export default SandboxDashboard
