import RangeSlider from '../../../common/components/RangeSlider'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import IncomeScheduler from '../../banking/components/IncomeScheduler'
import { useSliderAdjustmentV2 } from '../hooks/useSliderAdjustmentsV2'
import { useSliderConfig } from '../hooks/useSliderConfig'
import style from '../style/TontinatorInputs.module.scss'
import { TontinatorInputsProps } from '../types/TontinatorInputs.types'

const TontinatorInputs = ({
  formData,
  setFormData,
  skipComparePlanRangeAdjustment,
  sliderVariant,
  hideRetirementSliders,
  hideContributionSliders,
  hideCurrentAgeSlider,
  trackCurrentAgeSlider,
  trackRetirementSlider,
  trackOneTimeContribution,
  trackMonthlyContribution,
  forceDisableRetAgeDecrement,
  retirementSliderTestIds,
  showRetirementScheduler,
  oneTimeContributionTestIds,
}: TontinatorInputsProps) => {
  const {
    currentAgeSliderLabel,
    currentAgeSteps,
    disabledIncrementCurrentAge,
    disabledDecrementCurrentAge,
    retirementAgeSliderLabel,
    disableRetirementSliderLabel,
    disabledMonthlySliderLabel,
    retirementAgeSteps,
    disabledIncrementRetirementAge,
    disabledDecrementRetirementAge,
    retirementSliderEnabledSteps,
    oneTimeContributionLabel,
    oneTimeContributionSteps,
    shouldDisableDecOneTime,
    disabledIncOneTime,
    tontinatorParams,
    monthlyContributionLabel,
    monthlyContributionSteps,
    disabledDecMonthly,
    disabledIncMonthly,
    isRetired,
    shouldDisabledUSA,
    formatter,
  } = useSliderConfig({
    formData,
    skipComparePlanRangeAdjustment,
  })

  const {
    handleCurrentAge,
    handleRetirementAge,
    handleOneTime,
    handleMonthly,
  } = useSliderAdjustmentV2({
    formData,
    setFormData,
    params: tontinatorParams,
    skipComparePlanRangeAdjustment,
  })

  return (
    <section className={style['tontinator-inputs']}>
      {showRetirementScheduler && formData && (
        <IncomeScheduler
          setRetirementData={setFormData}
          retirementData={formData}
        />
      )}

      {!hideRetirementSliders && (
        <>
          {!hideCurrentAgeSlider && (
            <RangeSlider
              label={currentAgeSliderLabel}
              steps={currentAgeSteps}
              value={formData?.contributionAge?.age}
              onChange={handleCurrentAge}
              disabledIncrement={disabledIncrementCurrentAge}
              disabledDecrement={disabledDecrementCurrentAge}
              incrementButtonDataTestID={UI_TEST_ID.currentAgeIncrementButton}
              decrementButtonDataTestID={UI_TEST_ID.currentAgeDecrementButton}
              boxValueDataTestID={UI_TEST_ID.currentAgeSlider}
              variant={sliderVariant}
              trackSlider={trackCurrentAgeSlider}
            />
          )}

          <RangeSlider
            label={retirementAgeSliderLabel}
            steps={retirementAgeSteps}
            value={formData?.retirementAge?.age ?? 0}
            onChange={handleRetirementAge}
            disabledIncrement={disabledIncrementRetirementAge}
            disabledDecrement={
              forceDisableRetAgeDecrement || disabledDecrementRetirementAge
            }
            incrementButtonDataTestID={
              retirementSliderTestIds?.incrementButtonDataTestID ??
              UI_TEST_ID.retirementAgeIncrementButton
            }
            decrementButtonDataTestID={
              retirementSliderTestIds?.decrementButtonDataTestID ??
              UI_TEST_ID.retirementAgeDecrementButton
            }
            boxValueDataTestID={
              retirementSliderTestIds?.boxValueDataTestID ??
              UI_TEST_ID.retirementAgeSliderBox
            }
            sliderTestID={UI_TEST_ID.retirementSlider}
            trackSlider={trackRetirementSlider}
            variant={sliderVariant}
            enabledSteps={retirementSliderEnabledSteps}
            disabled={shouldDisabledUSA}
            disabledSliderTooltipText={disableRetirementSliderLabel}
          />
        </>
      )}
      {!hideContributionSliders && (
        <>
          <RangeSlider
            label={oneTimeContributionLabel}
            steps={oneTimeContributionSteps}
            value={formData?.oneTimeContribution}
            onChange={handleOneTime}
            disabledIncrement={disabledIncOneTime}
            disabledDecrement={shouldDisableDecOneTime()}
            trackSlider={trackOneTimeContribution}
            variant={sliderVariant}
            formatter={formatter}
            incrementButtonDataTestID={
              oneTimeContributionTestIds?.incrementButtonDataTestID ??
              UI_TEST_ID.retirementAgeIncrementButton
            }
            decrementButtonDataTestID={
              oneTimeContributionTestIds?.decrementButtonDataTestID ??
              UI_TEST_ID.retirementAgeDecrementButton
            }
            boxValueDataTestID={
              oneTimeContributionTestIds?.boxValueDataTestID ??
              UI_TEST_ID.retirementAgeSliderBox
            }
            sliderTestID={UI_TEST_ID.retirementSlider}
            disabled={shouldDisabledUSA}
          />

          <RangeSlider
            label={monthlyContributionLabel}
            steps={monthlyContributionSteps}
            value={formData?.monthlyContribution}
            onChange={handleMonthly}
            disabledIncrement={disabledIncMonthly}
            disabledDecrement={disabledDecMonthly}
            incrementButtonDataTestID={UI_TEST_ID.monthlyIncrementButton}
            decrementButtonDataTestID={UI_TEST_ID.monthlyDecrementButton}
            boxValueDataTestID={UI_TEST_ID.monthlySliderBox}
            trackSlider={trackMonthlyContribution}
            variant={sliderVariant}
            disabled={isRetired}
            disabledSliderTooltipText={disabledMonthlySliderLabel}
            formatter={formatter}
          />
        </>
      )}
    </section>
  )
}

export default TontinatorInputs
