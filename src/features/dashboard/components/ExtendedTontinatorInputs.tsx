import { t } from 'i18next'
import SelectSex from '../../../common/components/SelectSex'
import { ExtendedTontinatorInputsProps } from '../types/ExtendedTontinatorInputs.types'
import { paramModeToLabel } from '../utils/consts'
import InvStrategiesDropdown from './InvStrategiesDropdown'

const ExtendedTontinatorInputs = ({
  incomeForecastParams,
  setIncomeForecastParams,
  trackInvStrategies,
  trackSex,
  showSex,
  defaultOpen,
}: ExtendedTontinatorInputsProps) => {
  return (
    <>
      <InvStrategiesDropdown
        defaultOpen={defaultOpen}
        value={incomeForecastParams?.strategy}
        onChange={(strategy) => {
          setIncomeForecastParams({
            ...incomeForecastParams,
            strategy,
          })
        }}
        label={t(
          paramModeToLabel?.[incomeForecastParams?.paramsMode ?? 'TTF']
            ?.investmentStrategyLabel
        )}
        trackActivity={trackInvStrategies}
      />

      {showSex && (
        <SelectSex
          label={
            incomeForecastParams?.paramsMode === 'TTF'
              ? t('TTF_FORM.SELECT_SEX_LABEL')
              : t('SEX_FORM.SELECT_SEX_LABEL')
          }
          sex={incomeForecastParams?.sex}
          setSex={(sex) => {
            setIncomeForecastParams({
              ...incomeForecastParams,
              sex,
            })
          }}
          trackActivities={trackSex}
        />
      )}
    </>
  )
}

export default ExtendedTontinatorInputs
