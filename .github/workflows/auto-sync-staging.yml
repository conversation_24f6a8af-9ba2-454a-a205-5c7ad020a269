name: Push staging to staging-my-tontine-lite
on:
  push:
    branches:
      - staging
jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Push staging to staging-my-tontine-lite
        run: |
          git push origin staging:staging-my-tontine-lite --force
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
